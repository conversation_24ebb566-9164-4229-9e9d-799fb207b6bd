from aws_cdk import CfnOutput, Duration
from aws_cdk import aws_iam as iam
from aws_cdk import aws_s3 as s3
from constructs import Construct

from .config import (
    API_CORS_MAX_AGE_HOURS,
    S3_ACTIONS_PRESIGNED,
    S3_ACTIONS_READ_WRITE,
    S3_CORS_EXPOSED_HEADERS,
    S3_CORS_MAX_AGE_SECONDS,
)


def create_s3_read_write_policy(
    bucket_name: str, resource_prefix: str = ""
) -> iam.PolicyStatement:
    resources = [f"arn:aws:s3:::{bucket_name}"]
    if resource_prefix:
        resources.append(f"arn:aws:s3:::{bucket_name}/{resource_prefix}*")
    else:
        resources.append(f"arn:aws:s3:::{bucket_name}/*")

    return iam.PolicyStatement(
        effect=iam.Effect.ALLOW,
        actions=S3_ACTIONS_READ_WRITE,
        resources=resources,
    )


def create_s3_presigned_policy(
    bucket_arn: str, allowed_extensions: list[str] | None = None
) -> iam.PolicyStatement:
    statement = iam.PolicyStatement(
        effect=iam.Effect.ALLOW,
        actions=S3_ACTIONS_PRESIGNED,
        resources=[bucket_arn, f"{bucket_arn}/*"],
    )

    if allowed_extensions:
        statement.add_condition("StringLike", {"s3:prefix": allowed_extensions})

    return statement


def create_s3_cors_rules(allowed_origins: list[str]) -> list[s3.CorsRule]:
    return [
        s3.CorsRule(
            allowed_methods=[
                s3.HttpMethods.GET,
                s3.HttpMethods.PUT,
                s3.HttpMethods.POST,
                s3.HttpMethods.HEAD,
            ],
            allowed_origins=allowed_origins,
            allowed_headers=["*"],
            exposed_headers=S3_CORS_EXPOSED_HEADERS,
            max_age=S3_CORS_MAX_AGE_SECONDS,
        )
    ]


def create_api_cors_options(allowed_origins: list[str]) -> dict:
    from aws_cdk import aws_apigateway as apigw

    return {
        "allow_origins": [*allowed_origins, "https://*"],
        "allow_methods": apigw.Cors.ALL_METHODS,
        "allow_headers": [
            "Content-Type",
            "X-Amz-Date",
            "Authorization",
            "X-Api-Key",
            "X-Amz-Security-Token",
            "X-Amz-User-Agent",
        ],
        "allow_credentials": True,
        "max_age": Duration.hours(API_CORS_MAX_AGE_HOURS),
    }


def create_cfn_output(
    scope: Construct,
    id: str,
    value: str,
    description: str,
    export_name: str | None = None,
) -> CfnOutput:
    return CfnOutput(
        scope,
        id,
        value=value,
        description=description,
        export_name=export_name,
    )
