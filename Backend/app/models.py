from pydantic import BaseModel, <PERSON>, field_validator
from typing import Optional, List
from datetime import datetime
from .constants import MAX_FILE_SIZE_BYTES, ALLOWED_EXTENSIONS


class PresignedUrlRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to upload")
    filesize: int = Field(..., description="Size of the file in bytes")

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v):
        if not v.lower().endswith(ALLOWED_EXTENSIONS):
            raise ValueError("Only .edf files are allowed")
        return v

    @field_validator("filesize")
    @classmethod
    def validate_filesize(cls, v):
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError("File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


class PresignedUrlResponse(BaseModel):
    url: str = Field(..., description="Presigned URL for file upload")
    key: str = Field(..., description="S3 object key")
    expires_in: int = Field(..., description="URL expiry time in seconds")


class FileInfo(BaseModel):
    key: str = Field(..., description="S3 object key")
    filename: str = Field(..., description="Original filename")
    size: int = Field(..., description="File size in bytes")
    last_modified: datetime = Field(...,
                                    description="Last modification timestamp")
    etag: Optional[str] = Field(None, description="ETag of the object")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileListResponse(BaseModel):
    files: List[FileInfo] = Field(...,
                                  description="List of files in the bucket")
    total_count: int = Field(..., description="Total number of files")
    total_size: int = Field(...,
                            description="Total size of all files in bytes")


class FileDeleteRequest(BaseModel):
    key: str = Field(..., description="S3 object key to delete")


class FileDeleteResponse(BaseModel):
    success: bool = Field(..., description="Whether deletion was successful")
    message: str = Field(..., description="Status message")
    deleted_key: str = Field(..., description="Key of the deleted object")


class ErrorResponse(BaseModel):
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Additional error details")


# Multipart upload models

class MultipartInitiateRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to upload")
    filesize: int = Field(..., description="Size of the file in bytes")

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v):
        if not v.lower().endswith(ALLOWED_EXTENSIONS):
            raise ValueError("Only .edf files are allowed")
        return v

    @field_validator("filesize")
    @classmethod
    def validate_filesize(cls, v):
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError("File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


class MultipartInitiateResponse(BaseModel):
    upload_id: str = Field(..., description="S3 multipart upload ID")
    key: str = Field(..., description="S3 object key")
    expires_in: int = Field(...,
                            description="URL expiry time in seconds for part URLs")


class MultipartPartUrlRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="S3 multipart upload ID")
    part_number: int = Field(..., description="Part number (1-based)")


class MultipartPartUrlResponse(BaseModel):
    url: str = Field(..., description="Presigned URL for uploading a part")
    expires_in: int = Field(..., description="URL expiry time in seconds")


class CompletedPart(BaseModel):
    ETag: str = Field(..., description="ETag returned by S3 for this part")
    PartNumber: int = Field(..., description="Part number")


class MultipartCompleteRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="S3 multipart upload ID")
    parts: List[CompletedPart] = Field(...,
                                       description="List of uploaded parts")


class MultipartAbortRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="S3 multipart upload ID")
