MAX_FILE_SIZE_BYTES = 1024 * 1024 * 1024
MAX_FILE_SIZE_MB = 1024
ALLOWED_EXTENSIONS = (".edf", ".EDF")
DEFAULT_EXPIRY_SECONDS = 3600
CONTENT_TYPE_OCTET_STREAM = "application/octet-stream"
EDF_EXTENSION = ".edf"
EDF_FILES_PREFIX = "edf-files"

API_PREFIX = "/api/v1"
API_TITLE = "Biormika EDF File Upload API"
API_DESCRIPTION = "API for uploading and managing EDF files for HFO analysis"
API_VERSION = "1.0.0"

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_HEADERS = ["*"]

LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOGGING_LEVEL = "INFO"

S3_CLIENT_METHOD_PUT = "put_object"
S3_CLIENT_METHOD_GET = "get_object"
S3_CLIENT_METHOD_UPLOAD_PART = "upload_part"
S3_LIST_OBJECTS_METHOD = "list_objects_v2"
S3_ERROR_NOT_FOUND = "404"

METADATA_ORIGINAL_FILENAME = "original-filename"

HTTP_STATUS_OK = 200
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_INTERNAL_SERVER_ERROR = 500

LAMBDA_RESPONSE_HEADERS = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
}
