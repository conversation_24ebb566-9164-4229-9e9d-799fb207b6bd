"""
Shared constants used across Backend and Infrastructure.
This module centralizes configuration to avoid duplication.
"""

# AWS Configuration
AWS_PROFILE = "biormika"
AWS_REGION = "us-east-1"

# Application Configuration
PROJECT_NAME = "Biormika"
ENVIRONMENT = "Development"
PURPOSE = "EDF-File-Processing"

# Network Configuration
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS Configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]

# File Configuration
MAX_FILE_SIZE_MB = 1024  # 1GB
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
ALLOWED_FILE_EXTENSIONS = [".edf", ".EDF"]
PRESIGNED_URL_EXPIRY_SECONDS = 3600

# S3 Configuration
S3_PREFIX = "edf-files/"
S3_CORS_MAX_AGE_SECONDS = 3600
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]

# Stack Configuration
STACK_NAME = "BiormikaStack"
STACK_DESCRIPTION = "Biormika EDF file upload and management infrastructure"