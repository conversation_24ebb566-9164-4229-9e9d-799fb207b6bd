import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON>rror
from typing import List, Optional, Dict

from ..config import settings
from ..models import FileInfo
from ..logging_config import get_logger
from ..constants import (
    DEFAULT_EXPIRY_SECONDS,
    EDF_EXTENSION,
    S3_LIST_OBJECTS_METHOD,
    S3_ERROR_NOT_FOUND,
    CONTENT_TYPE_OCTET_STREAM,
)
from .presigned_url_service import PresignedUrlService

logger = get_logger(__name__)


class S3Service:
    def __init__(self):
        config = None
        if settings.s3_transfer_acceleration:
            from botocore.config import Config
            config = Config(s3={"use_accelerate_endpoint": True})
        self.s3_client = boto3.client(
            "s3", region_name=settings.aws_region, config=config)
        self.bucket_name = settings.s3_bucket_name
        self.url_service = PresignedUrlService(
            self.s3_client, self.bucket_name)

    def generate_presigned_url(self, key: str, filename: str) -> str:
        return self.url_service.generate_upload_url(key, filename)

    def list_files(self, prefix: Optional[str] = None) -> List[FileInfo]:
        """List all files in the S3 bucket"""
        try:
            paginator = self.s3_client.get_paginator(S3_LIST_OBJECTS_METHOD)
            page_iterator = paginator.paginate(
                Bucket=self.bucket_name, Prefix=prefix or ""
            )

            files = []
            for page in page_iterator:
                if "Contents" in page:
                    for obj in page["Contents"]:
                        if obj["Key"].lower().endswith(EDF_EXTENSION):
                            files.append(
                                FileInfo(
                                    key=obj["Key"],
                                    filename=obj["Key"].split("/")[-1],
                                    size=obj["Size"],
                                    last_modified=obj["LastModified"],
                                    etag=obj.get("ETag", "").strip('"'),
                                )
                            )

            return files
        except ClientError as e:
            logger.error(f"Error listing files: {e}")
            raise

    def delete_file(self, key: str) -> bool:
        """Delete a file from S3"""
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            logger.error(f"Error deleting file {key}: {e}")
            raise

    def get_file_metadata(self, key: str) -> Optional[Dict]:
        """Get metadata for a specific file"""
        try:
            response = self.s3_client.head_object(
                Bucket=self.bucket_name, Key=key)
            return {
                "size": response["ContentLength"],
                "last_modified": response["LastModified"],
                "etag": response.get("ETag", "").strip('"'),
                "metadata": response.get("Metadata", {}),
            }
        except ClientError as e:
            if e.response["Error"]["Code"] == S3_ERROR_NOT_FOUND:
                return None
            logger.error(f"Error getting file metadata: {e}")
            raise

    def generate_download_url(
        self, key: str, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        return self.url_service.generate_download_url(key, expiry)

    # Multipart upload helpers
    def initiate_multipart_upload(self, key: str, filename: str) -> str:
        response = self.s3_client.create_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            ContentType=CONTENT_TYPE_OCTET_STREAM,
            Metadata={"original-filename": filename},
        )
        return response["UploadId"]

    def generate_part_url(self, key: str, upload_id: str, part_number: int) -> str:
        return self.url_service.generate_multipart_part_url(key, upload_id, part_number)

    def complete_multipart_upload(self, key: str, upload_id: str, parts: list[dict]):
        self.s3_client.complete_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            UploadId=upload_id,
            MultipartUpload={"Parts": parts},
        )

    def abort_multipart_upload(self, key: str, upload_id: str):
        self.s3_client.abort_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            UploadId=upload_id,
        )


s3_service = S3Service()
