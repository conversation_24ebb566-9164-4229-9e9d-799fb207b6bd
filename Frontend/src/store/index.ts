import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import filesReducer from './slices/filesSlice';
import analysisReducer from './slices/analysisSlice';

export const store = configureStore({
  reducer: {
    user: userReducer,
    files: filesReducer,
    analysis: analysisReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;