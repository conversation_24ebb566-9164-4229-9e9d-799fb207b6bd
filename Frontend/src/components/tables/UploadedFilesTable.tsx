import { useAppSelector } from "@/store/hooks";
import { selectFiles } from "@/store/slices/filesSlice";
import { DataTable, type TableColumn } from "./DataTable";

interface UploadedFile {
  edf: string;
  size: string;
  patient: string;
  srHz: number;
  recordingStartDate: string;
  recordingStartTime: string;
  recordingEndDate: string;
  recordingEndTime: string;
  recordingLength: string;
}

const columns: TableColumn<UploadedFile>[] = [
  { key: "edf", header: "EDF", accessor: (file) => file.edf },
  { key: "size", header: "Size", accessor: (file) => file.size },
  { key: "patient", header: "Patient", accessor: (file) => file.patient },
  { key: "srHz", header: "SR (Hz)", accessor: (file) => String(file.srHz) },
  { key: "recordingStartDate", header: "Recording start date", accessor: (file) => file.recordingStartDate },
  { key: "recordingStartTime", header: "Recording start time", accessor: (file) => file.recordingStartTime },
  { key: "recordingEndDate", header: "Recording end date", accessor: (file) => file.recordingEndDate },
  { key: "recordingEndTime", header: "Recording end time", accessor: (file) => file.recordingEndTime },
  { key: "recordingLength", header: "Recording length", accessor: (file) => file.recordingLength },
];

export function UploadedFilesTable() {
  const { uploadedFiles, isLoading } = useAppSelector(selectFiles);

  return (
    <DataTable
      title="Files Uploaded (Ready to be Analyzed)"
      columns={columns}
      data={uploadedFiles}
      loading={isLoading}
      emptyMessage="No uploaded files available"
      getRowKey={(file) => file.edf}
    />
  );
}