import React, { useEffect } from "react";
import { Download, Trash2, FileIcon, RefreshCw } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { useFileActions } from "@/hooks/useFileActions";
import { formatFileSize, formatDate } from "@/utils/file";
import { useToast } from "@/hooks/useToast";
import { DataTable, type TableColumn } from "./tables/DataTable";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import type { FileInfo } from "@/types/api";

interface FileManagerProps {
  refreshTrigger?: number;
}

export const FileManager: React.FC<FileManagerProps> = ({ 
  refreshTrigger 
}) => {
  const { toast, hideToast } = useToast();
  const { dialogState } = useConfirmDialog();
  const { files, loading, error, fetchFiles } = useFileOperations();
  const { handleDownload, handleDelete, isOperationInProgress } = useFileActions({
    onDeleteSuccess: fetchFiles,
  });

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  const columns: TableColumn<FileInfo>[] = [
    {
      key: "filename",
      header: "File Name",
      accessor: (file) => (
        <div className="flex items-center">
          <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">{file.filename}</span>
        </div>
      ),
    },
    {
      key: "size",
      header: "Size",
      accessor: (file) => (
        <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>
      ),
    },
    {
      key: "modified",
      header: "Modified",
      accessor: (file) => (
        <span className="text-sm text-gray-500">{formatDate(file.last_modified)}</span>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      className: "text-right",
      accessor: (file) => (
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleDownload(file.key)}
            disabled={isOperationInProgress("download", file.key)}
            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
            title="Download"
          >
            {isOperationInProgress("download", file.key) ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Download className="w-4 h-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(file.key, file.filename)}
            disabled={isOperationInProgress("delete", file.key)}
            className="text-red-600 hover:text-red-900 disabled:opacity-50"
            title="Delete"
          >
            {isOperationInProgress("delete", file.key) ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      {toast.isOpen && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
      <ConfirmDialog {...dialogState} />
      
      <DataTable
        title={`Files Uploaded (Ready to be Analyzed) (${files.length})`}
        columns={columns}
        data={files}
        loading={loading}
        error={error}
        emptyMessage="No uploaded files available"
        onRefresh={fetchFiles}
        getRowKey={(file) => file.key}
      />
    </>
  );
};