export interface AnalyzedFile {
  edf: string;
  size: string;
  patient: string;
  srHz: number;
  recordingDate: string;
  recordingStartTime: string;
  dateAnalyzed: string;
  analyzedLength: string;
  analyzedMontage: string;
  analyzedBand: string;
  parametersUsed: string;
}

export interface UploadedFile {
  edf: string;
  size: string;
  patient: string;
  srHz: number;
  recordingStartDate: string;
  recordingStartTime: string;
  recordingEndDate: string;
  recordingEndTime: string;
  recordingLength: string;
}

export interface FilesState {
  uploadedFiles: UploadedFile[];
  analyzedFiles: AnalyzedFile[];
  selectedFile: UploadedFile | null;
  isLoading: boolean;
  error: string | null;
  operations: {
    uploading: boolean;
    deleting: string | null;
    fetchingDownload: string | null;
  };
}

export type FileUploadStatus = "idle" | "uploading" | "success" | "error";

export interface FileValidationResult {
  valid: File[];
  errors: string[];
}

export interface ValidationResult {
  valid: boolean;
  error?: string;
}