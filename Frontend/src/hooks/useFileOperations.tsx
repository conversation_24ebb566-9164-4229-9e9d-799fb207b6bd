import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  fetchFilesThunk, 
  deleteFileThunk, 
  getDownloadUrlThunk,
  selectFiles 
} from "@/store/slices/filesSlice";
import { useToast } from "./useToast";
import { SUCCESS_MESSAGES } from "@/constants";
import type { FileInfo } from "@/types/api";

export function useFileOperations() {
  const dispatch = useAppDispatch();
  const { uploadedFiles, isLoading, error, operations } = useAppSelector(selectFiles);
  const { showToast } = useToast();

  const files: FileInfo[] = uploadedFiles.map(file => ({
    key: file.edf,
    filename: file.edf,
    size: parseFloat(file.size.replace(' MB', '')) * 1024 * 1024,
    last_modified: `${file.recordingStartDate}T${file.recordingStartTime}`,
    etag: undefined,
  }));

  const fetchFiles = useCallback(async () => {
    try {
      await dispatch(fetchFilesThunk()).unwrap();
    } catch {
      showToast(error || "Failed to fetch files", "error");
    }
  }, [dispatch, error, showToast]);

  const deleteFile = useCallback(
    async (key: string) => {
      try {
        await dispatch(deleteFileThunk(key)).unwrap();
        showToast(SUCCESS_MESSAGES.FILE_DELETED, "success");
        await dispatch(fetchFilesThunk()).unwrap();
        return true;
      } catch {
        showToast(error || "Failed to delete file", "error");
        return false;
      }
    },
    [dispatch, error, showToast]
  );

  const getDownloadUrl = useCallback(
    async (key: string) => {
      try {
        const result = await dispatch(getDownloadUrlThunk(key)).unwrap();
        return result.url;
      } catch {
        showToast("Failed to generate download link", "error");
        return null;
      }
    },
    [dispatch, showToast]
  );

  return {
    files,
    loading: isLoading,
    error,
    deletingKey: operations.deleting,
    fetchFiles,
    deleteFile,
    getDownloadUrl,
  };
}