import { useCallback, useState } from "react";
import { useToast } from "./useToast";
import { useConfirmDialog } from "./useConfirmDialog";
import { fileOperations } from "@/services/fileOperationsService";
import { SUCCESS_MESSAGES } from "@/constants";

interface UseFileActionsOptions {
  onDeleteSuccess?: () => void;
  onDownloadSuccess?: () => void;
}

export function useFileActions(options: UseFileActionsOptions = {}) {
  const [operationInProgress, setOperationInProgress] = useState<string | null>(null);
  const { showToast } = useToast();
  const { showConfirmDialog } = useConfirmDialog();

  const handleDownload = useCallback(
    async (key: string) => {
      setOperationInProgress(`download-${key}`);
      try {
        const result = await fileOperations.getDownloadUrl(key);
        if (result.success && result.data) {
          window.open(result.data, "_blank");
          options.onDownloadSuccess?.();
        } else {
          showToast(result.error || "Download failed", "error");
        }
      } finally {
        setOperationInProgress(null);
      }
    },
    [showToast, options]
  );

  const handleDelete = useCallback(
    (key: string, filename: string) => {
      showConfirmDialog(
        "Delete File",
        `Are you sure you want to delete ${filename}?`,
        async () => {
          setOperationInProgress(`delete-${key}`);
          try {
            const result = await fileOperations.deleteFile(key);
            if (result.success) {
              showToast(SUCCESS_MESSAGES.FILE_DELETED, "success");
              options.onDeleteSuccess?.();
            } else {
              showToast(result.error || "Delete failed", "error");
            }
          } finally {
            setOperationInProgress(null);
          }
        },
        {
          confirmText: "Delete",
          cancelText: "Cancel",
          isDestructive: true,
        }
      );
    },
    [showConfirmDialog, showToast, options]
  );

  const isOperationInProgress = useCallback(
    (type: "download" | "delete", key: string) => {
      return operationInProgress === `${type}-${key}`;
    },
    [operationInProgress]
  );

  return {
    handleDownload,
    handleDelete,
    isOperationInProgress,
    operationInProgress,
  };
}