import { ERROR_MESSAGES } from "../constants";
import axios from "axios";

export interface AppError {
  message: string;
  code?: string;
  details?: unknown;
}

export const handleError = (error: unknown): AppError => {
  if (axios.isAxiosError(error)) {
    if (error.response?.data?.detail) {
      return { message: error.response.data.detail, code: String(error.response.status), details: error };
    }
    if (error.response?.data?.message) {
      return { message: error.response.data.message, code: String(error.response.status), details: error };
    }
    if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
      return { message: "Request timed out. Please try again.", code: "TIMEOUT", details: error };
    }
    if (!error.response) {
      return { message: ERROR_MESSAGES.NETWORK_ERROR, code: "NETWORK_ERROR", details: error };
    }
  }
  
  if (error instanceof Error) {
    if (error.message.includes("Network")) {
      return { message: ERROR_MESSAGES.NETWORK_ERROR, code: "NETWORK_ERROR", details: error };
    }
    return { message: error.message, details: error };
  }
  
  if (typeof error === "string") {
    return { message: error };
  }
  
  if (error && typeof error === "object" && "message" in error) {
    return { message: String(error.message), details: error };
  }
  
  return { message: ERROR_MESSAGES.UNKNOWN_ERROR, details: error };
};

export const getErrorMessage = (error: unknown): string => {
  return handleError(error).message;
};

export const handleApiError = (error: unknown, defaultMessage: string): string => {
  const message = getErrorMessage(error);
  return message || defaultMessage;
};