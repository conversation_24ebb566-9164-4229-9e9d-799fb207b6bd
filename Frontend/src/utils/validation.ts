import { FILE_CONFIG, ERROR_MESSAGES } from "@/constants";
import type { ValidationResult, FileValidationResult } from "@/types";

const getFileExtension = (filename: string): string => {
  return `.${filename.split(".").pop()?.toLowerCase()}`;
};

const validateFile = (
  file: File,
  maxSize: number,
  allowedExtensions: string[]
): ValidationResult => {
  const extension = getFileExtension(file.name);
  
  if (!allowedExtensions.includes(extension)) {
    return {
      valid: false,
      error: ERROR_MESSAGES.INVALID_FILE_TYPE,
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: ERROR_MESSAGES.FILE_TOO_LARGE,
    };
  }
  
  return { valid: true };
};

export const validateEDFFile = (file: File): ValidationResult => {
  return validateFile(file, FILE_CONFIG.MAX_SIZE_BYTES, FILE_CONFIG.ALLOWED_EXTENSIONS);
};

export const validateMultipleFiles = (
  files: File[],
  maxSize: number = FILE_CONFIG.MAX_SIZE_BYTES,
  allowedExtensions: string[] = FILE_CONFIG.ALLOWED_EXTENSIONS
): FileValidationResult => {
  const valid: File[] = [];
  const errors: string[] = [];

  files.forEach((file) => {
    const result = validateFile(file, maxSize, allowedExtensions);
    if (result.valid) {
      valid.push(file);
    } else {
      errors.push(`${file.name}: ${result.error}`);
    }
  });

  return { valid, errors };
};