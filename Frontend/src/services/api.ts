import axios from "axios";
import { API_CONFIG } from "../constants";
import type {
  PresignedUrlRequest,
  PresignedUrlResponse,
  FileListResponse,
  FileDeleteResponse,
  MultipartInitiateResponse,
  MultipartPartUrlResponse,
  CompletedPart,
} from "../types/api";

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export const fileService = {
  async getPresignedUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    const response = await api.post<PresignedUrlResponse>(`${API_CONFIG.ENDPOINTS.FILES}/generate-presigned-url`, request);
    return response.data;
  },

  async uploadFile(file: File, presignedUrl: string, onProgress?: (percent: number) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.open("PUT", presignedUrl);
      xhr.setRequestHeader("Content-Type", "application/octet-stream");

      xhr.upload.onprogress = (event: ProgressEvent) => {
        if (event.lengthComputable && onProgress) {
          const percent = Math.round((event.loaded / event.total) * 100);
          onProgress(percent);
        }
      };

      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status: ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error("Network error during upload"));
      xhr.send(file);
    });
  },

  // Multipart endpoints
  async initiateMultipart(filename: string, filesize: number): Promise<MultipartInitiateResponse> {
    const response = await api.post<MultipartInitiateResponse>(`${API_CONFIG.ENDPOINTS.FILES}/multipart/initiate`, { filename, filesize });
    return response.data;
  },

  async getPartUploadUrl(key: string, uploadId: string, partNumber: number): Promise<MultipartPartUrlResponse> {
    const payload: { key: string; upload_id: string; part_number: number } = {
      key,
      upload_id: uploadId,
      part_number: partNumber,
    };
    const response = await api.post<MultipartPartUrlResponse>(`${API_CONFIG.ENDPOINTS.FILES}/multipart/part-url`, payload);
    return response.data;
  },

  async completeMultipart(key: string, uploadId: string, parts: CompletedPart[]): Promise<void> {
    await api.post(`${API_CONFIG.ENDPOINTS.FILES}/multipart/complete`, { key, upload_id: uploadId, parts });
  },

  async abortMultipart(key: string, uploadId: string): Promise<void> {
    await api.post(`${API_CONFIG.ENDPOINTS.FILES}/multipart/abort`, { key, upload_id: uploadId });
  },

  async listFiles(): Promise<FileListResponse> {
    const response = await api.get<FileListResponse>(`${API_CONFIG.ENDPOINTS.FILES}/list`);
    return response.data;
  },

  async deleteFile(key: string): Promise<FileDeleteResponse> {
    const response = await api.delete<FileDeleteResponse>(`${API_CONFIG.ENDPOINTS.FILES}/delete`, { data: { key } });
    return response.data;
  },

  async getDownloadUrl(key: string): Promise<{ download_url: string; filename: string }> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.FILES}/download/${encodeURIComponent(key)}`);
    return response.data;
  },
};

export default api;
