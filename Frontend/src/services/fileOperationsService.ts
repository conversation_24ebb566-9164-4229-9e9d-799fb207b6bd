import { fileService } from "./api";
import { ERROR_MESSAGES, FILE_CONFIG } from "@/constants";
import type { CompletedPart } from "@/types/api";
import type { FileInfo } from "@/types/api";

export type OperationResult<T = void> = {
  success: boolean;
  data?: T;
  error?: string;
};

export class FileOperationsService {
  static async uploadFile(file: File, onProgress?: (percent: number) => void): Promise<OperationResult> {
    try {
      // Initiate multipart upload
      const { upload_id, key } = await fileService.initiateMultipart(file.name, file.size);

      const MB = 1024 * 1024;
      const minChunk = 5 * MB;
      const targetChunk = file.size >= 400 * MB ? 32 * MB : FILE_CONFIG.CHUNK_SIZE; // 32MB for >=400MB, else default (8MB)
      const chunkSize = Math.max(minChunk, Math.min(targetChunk, file.size));
      const totalParts = Math.ceil(file.size / chunkSize);
      const concurrency = Math.min(8, totalParts);
      const parts: CompletedPart[] = [];

      let uploadedBytes = 0;
      const updateProgress = () => {
        if (!onProgress) return;
        const percent = Math.min(100, Math.round((uploadedBytes / file.size) * 100));
        onProgress(percent);
      };

      const uploadPart = async (partNumber: number) => {
        const start = (partNumber - 1) * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const blob = file.slice(start, end);
        const { url } = await fileService.getPartUploadUrl(key, upload_id, partNumber);

        // Use XHR for per-part progress
        const eTag = await new Promise<string>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          let lastLoaded = 0;
          xhr.open("PUT", url);
          xhr.upload.onprogress = (ev: ProgressEvent) => {
            if (ev.lengthComputable) {
              const delta = ev.loaded - lastLoaded;
              lastLoaded = ev.loaded;
              uploadedBytes += delta;
              updateProgress();
            }
          };
          xhr.onerror = () => reject(new Error(`Network error uploading part ${partNumber}`));
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const header = xhr.getResponseHeader("ETag") || "";
              resolve(header.replaceAll('"', ""));
            } else {
              reject(new Error(`Part ${partNumber} upload failed (${xhr.status})`));
            }
          };
          xhr.send(blob);
        });

        parts.push({ ETag: eTag, PartNumber: partNumber });
      };

      // Simple worker pool
      const queue = Array.from({ length: totalParts }, (_, i) => i + 1);
      const workers = Array.from({ length: Math.min(concurrency, totalParts) }, async () => {
        while (queue.length) {
          const partNumber = queue.shift();
          if (!partNumber) break;
          await uploadPart(partNumber);
        }
      });
      await Promise.all(workers);

      // Sort parts and complete
      parts.sort((a, b) => a.PartNumber - b.PartNumber);
      await fileService.completeMultipart(key, upload_id, parts);
      onProgress?.(100);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.UPLOAD_FAILED,
      };
    }
  }

  static async deleteFile(key: string): Promise<OperationResult> {
    try {
      await fileService.deleteFile(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DELETE_FAILED,
      };
    }
  }

  static async fetchFiles(): Promise<OperationResult<FileInfo[]>> {
    try {
      const response = await fileService.listFiles();
      return { success: true, data: response.files };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.FETCH_FAILED,
      };
    }
  }

  static async getDownloadUrl(key: string): Promise<OperationResult<string>> {
    try {
      const response = await fileService.getDownloadUrl(key);
      return { success: true, data: response.download_url };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DOWNLOAD_FAILED,
      };
    }
  }

  static async performFileAction<T>(action: () => Promise<T>, errorMessage?: string): Promise<OperationResult<T>> {
    try {
      const result = await action();
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : errorMessage || "Operation failed",
      };
    }
  }
}

export const fileOperations = FileOperationsService;
